{"moduleFileExtensions": ["js", "json", "ts"], "rootDir": ".", "testEnvironment": "node", "testRegex": ".e2e-spec.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["src/**/*.(t|j)s"], "coverageDirectory": "./coverage", "moduleNameMapper": {"^@enterprise/common$": "<rootDir>/../../../libs/common/src", "^@enterprise/event-bus$": "<rootDir>/../../../libs/event-bus/src", "^@enterprise/event-store$": "<rootDir>/../../../libs/event-store/src"}, "setupFilesAfterEnv": ["<rootDir>/test-setup.ts"], "testTimeout": 30000}
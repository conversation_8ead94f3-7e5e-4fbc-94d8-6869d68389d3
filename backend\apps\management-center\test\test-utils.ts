import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication } from '@nestjs/common';
import { MongooseModule, getModelToken } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { JwtService } from '@nestjs/jwt';
import { User, UserDocument, UserRole, UserStatus } from '../src/auth/schemas/user.schema';

export interface TestUser {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  role: UserRole;
  status: UserStatus;
  token?: string;
}

export class TestUserFactory {
  constructor(
    private userModel: Model<UserDocument>,
    private jwtService: JwtService,
  ) {}

  async createUser(userData: Partial<User> = {}): Promise<TestUser> {
    const defaultUser = {
      email: `test-${Date.now()}@example.com`,
      password: 'hashedpassword123', // <PERSON><PERSON> hashed password
      firstName: 'Test',
      lastName: 'User',
      role: UserRole.USER,
      status: UserStatus.ACTIVE,
      emailVerified: true,
      ...userData,
    };

    const user = await this.userModel.create(defaultUser);
    const token = this.jwtService.sign({
      sub: user._id,
      email: user.email,
      role: user.role,
    });

    return {
      id: user._id.toString(),
      email: user.email,
      firstName: user.firstName,
      lastName: user.lastName,
      role: user.role,
      status: user.status,
      token,
    };
  }

  async createAdmin(userData: Partial<User> = {}): Promise<TestUser> {
    return this.createUser({
      role: UserRole.ADMIN,
      email: `admin-${Date.now()}@example.com`,
      firstName: 'Admin',
      lastName: 'User',
      ...userData,
    });
  }

  async createManager(userData: Partial<User> = {}): Promise<TestUser> {
    return this.createUser({
      role: UserRole.MANAGER,
      email: `manager-${Date.now()}@example.com`,
      firstName: 'Manager',
      lastName: 'User',
      ...userData,
    });
  }

  async createSuperAdmin(userData: Partial<User> = {}): Promise<TestUser> {
    return this.createUser({
      role: UserRole.SUPER_ADMIN,
      email: `superadmin-${Date.now()}@example.com`,
      firstName: 'Super',
      lastName: 'Admin',
      ...userData,
    });
  }
}

export class DatabaseTestUtils {
  constructor(private userModel: Model<UserDocument>) {}

  async clearUsers(): Promise<void> {
    await this.userModel.deleteMany({});
  }

  async getUserById(id: string): Promise<UserDocument | null> {
    return this.userModel.findById(id).exec();
  }

  async getUserByEmail(email: string): Promise<UserDocument | null> {
    return this.userModel.findOne({ email }).exec();
  }

  async getUserCount(): Promise<number> {
    return this.userModel.countDocuments({}).exec();
  }

  async getActiveUserCount(): Promise<number> {
    return this.userModel.countDocuments({ 
      status: UserStatus.ACTIVE,
      deletedAt: { $exists: false }
    }).exec();
  }
}

export async function createTestingModule(imports: any[] = []): Promise<{
  module: TestingModule;
  app: INestApplication;
  userFactory: TestUserFactory;
  dbUtils: DatabaseTestUtils;
}> {
  const defaultImports = [
    MongooseModule.forRoot(process.env.MONGODB_URI_WRITE || 'mongodb://localhost:27017/test', {
      connectionName: 'write',
    }),
    MongooseModule.forRoot(process.env.MONGODB_URI_READ || 'mongodb://localhost:27017/test', {
      connectionName: 'read',
    }),
  ];

  const module: TestingModule = await Test.createTestingModule({
    imports: [...defaultImports, ...imports],
  }).compile();

  const app = module.createNestApplication();
  
  const userModel = module.get<Model<UserDocument>>(getModelToken(User.name, 'write'));
  const jwtService = module.get<JwtService>(JwtService);
  
  const userFactory = new TestUserFactory(userModel, jwtService);
  const dbUtils = new DatabaseTestUtils(userModel);

  return { module, app, userFactory, dbUtils };
}

export const mockUser = (overrides: Partial<User> = {}): User => ({
  email: '<EMAIL>',
  password: 'hashedpassword',
  firstName: 'Test',
  lastName: 'User',
  role: UserRole.USER,
  status: UserStatus.ACTIVE,
  emailVerified: true,
  createdAt: new Date(),
  updatedAt: new Date(),
  isActive: () => true,
  hasRole: (role: UserRole) => role === UserRole.USER,
  hasMinimumRole: (role: UserRole) => {
    const roleHierarchy = {
      [UserRole.USER]: 1,
      [UserRole.MANAGER]: 2,
      [UserRole.ADMIN]: 3,
      [UserRole.SUPER_ADMIN]: 4,
    };
    return roleHierarchy[UserRole.USER] >= roleHierarchy[role];
  },
  ...overrides,
} as User);

export const mockUserDocument = (overrides: Partial<UserDocument> = {}): Partial<UserDocument> => ({
  _id: '507f1f77bcf86cd799439011',
  ...mockUser(),
  toJSON: () => mockUser(),
  toObject: () => mockUser(),
  save: jest.fn().mockResolvedValue(mockUser()),
  ...overrides,
});

// Custom Jest matchers for MongoDB ObjectIds and common patterns
export const customMatchers = {
  toBeValidObjectId: (received: string) => {
    const pass = /^[0-9a-fA-F]{24}$/.test(received);
    return {
      message: () => `expected ${received} ${pass ? 'not ' : ''}to be a valid ObjectId`,
      pass,
    };
  },

  toHaveValidTimestamps: (received: any) => {
    const hasCreatedAt = received.createdAt instanceof Date;
    const hasUpdatedAt = received.updatedAt instanceof Date;
    const pass = hasCreatedAt && hasUpdatedAt;
    
    return {
      message: () => `expected object ${pass ? 'not ' : ''}to have valid createdAt and updatedAt timestamps`,
      pass,
    };
  },

  toBeValidUserResponse: (received: any) => {
    const requiredFields = ['id', 'email', 'firstName', 'lastName', 'role', 'status'];
    const hasAllFields = requiredFields.every(field => received.hasOwnProperty(field));
    const hasNoPassword = !received.hasOwnProperty('password');
    const pass = hasAllFields && hasNoPassword;
    
    return {
      message: () => `expected object ${pass ? 'not ' : ''}to be a valid user response`,
      pass,
    };
  },
};

// Helper function to wait for async operations
export const waitFor = (ms: number): Promise<void> => 
  new Promise(resolve => setTimeout(resolve, ms));

// Helper function to generate random test data
export const generateTestData = {
  email: () => `test-${Date.now()}-${Math.random().toString(36).substr(2, 9)}@example.com`,
  name: () => `Test${Math.random().toString(36).substr(2, 5)}`,
  password: () => 'password123',
  phone: () => `+1${Math.floor(Math.random() * 9000000000) + 1000000000}`,
};

import { customMatchers } from './test-utils';

// Extend Jest with custom matchers
expect.extend(customMatchers);

// Global test configuration
beforeEach(() => {
  // Clear all mocks before each test
  jest.clearAllMocks();
});

// Global error handling for unhandled promise rejections
process.on('unhandledRejection', (reason, promise) => {
  console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});

// Increase timeout for integration tests
jest.setTimeout(30000);

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  // Uncomment to suppress console.log in tests
  // log: jest.fn(),
  // info: jest.fn(),
  // warn: jest.fn(),
  // error: jest.fn(),
};

// Global test utilities
global.testConfig = {
  jwtSecret: 'test-jwt-secret',
  jwtExpiresIn: '1h',
  mongoUri: 'mongodb://localhost:27017/test',
};

declare global {
  namespace jest {
    interface Matchers<R> {
      toBeValidObjectId(): R;
      toHaveValidTimestamps(): R;
      toBeValidUserResponse(): R;
    }
  }
  
  var testConfig: {
    jwtSecret: string;
    jwtExpiresIn: string;
    mongoUri: string;
  };
}

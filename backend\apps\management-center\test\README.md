# Testing Guide for Management Center

This document provides comprehensive information about testing the Management Center microservice.

## Test Structure

```
test/
├── jest-e2e.json          # E2E test configuration
├── jest-setup.ts          # Unit test setup
├── test-setup.ts          # E2E test setup
├── test-utils.ts          # Test utilities and factories
├── users.e2e-spec.ts      # E2E tests for users API
└── README.md              # This file

src/
├── users/
│   ├── users.controller.spec.ts  # Unit tests for controller
│   └── users.service.spec.ts     # Unit tests for service
```

## Test Types

### Unit Tests
- **Location**: `src/**/*.spec.ts`
- **Purpose**: Test individual components in isolation
- **Coverage**: Controllers, Services, Guards, Decorators
- **Mocking**: External dependencies are mocked

### E2E Tests
- **Location**: `test/**/*.e2e-spec.ts`
- **Purpose**: Test complete API workflows
- **Coverage**: HTTP endpoints, authentication, database integration
- **Database**: Uses MongoDB Memory Server for isolation

## Running Tests

### Unit Tests
```bash
# Run all unit tests
npm run test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:cov

# Debug tests
npm run test:debug
```

### E2E Tests
```bash
# Run all e2e tests
npm run test:e2e

# Run specific e2e test file
npm run test:e2e -- users.e2e-spec.ts
```

## Test Configuration

### Jest Configuration
- **Unit Tests**: `jest.config.js`
- **E2E Tests**: `test/jest-e2e.json`

### Environment Variables
E2E tests use the following environment variables:
- `MONGODB_URI_WRITE`: MongoDB connection for write operations
- `MONGODB_URI_READ`: MongoDB connection for read operations
- `JWT_SECRET`: JWT secret for token generation
- `JWT_EXPIRES_IN`: JWT token expiration time

## Test Utilities

### TestUserFactory
Creates test users with different roles:
```typescript
const userFactory = new TestUserFactory(userModel, jwtService);

// Create regular user
const user = await userFactory.createUser();

// Create admin user
const admin = await userFactory.createAdmin();

// Create user with custom data
const customUser = await userFactory.createUser({
  email: '<EMAIL>',
  role: UserRole.MANAGER
});
```

### DatabaseTestUtils
Provides database utilities for testing:
```typescript
const dbUtils = new DatabaseTestUtils(userModel);

// Clear all users
await dbUtils.clearUsers();

// Get user by ID
const user = await dbUtils.getUserById(userId);

// Get active user count
const count = await dbUtils.getActiveUserCount();
```

### Custom Jest Matchers
Extended Jest matchers for common assertions:
```typescript
// Check if string is valid ObjectId
expect(userId).toBeValidObjectId();

// Check if object has valid timestamps
expect(user).toHaveValidTimestamps();

// Check if response is valid user response
expect(response.body.data).toBeValidUserResponse();
```

## Test Data Generation

### Mock Data
```typescript
import { mockUser, mockUserDocument } from './test-utils';

// Create mock user
const user = mockUser({ role: UserRole.ADMIN });

// Create mock user document
const userDoc = mockUserDocument({ email: '<EMAIL>' });
```

### Random Test Data
```typescript
import { generateTestData } from './test-utils';

const testUser = {
  email: generateTestData.email(),
  firstName: generateTestData.name(),
  password: generateTestData.password(),
  phone: generateTestData.phone(),
};
```

## Testing Best Practices

### Unit Tests
1. **Isolation**: Mock all external dependencies
2. **Coverage**: Test all code paths including error cases
3. **Assertions**: Use specific assertions for better error messages
4. **Setup**: Use `beforeEach` for test setup, `afterEach` for cleanup

### E2E Tests
1. **Database**: Use in-memory database for isolation
2. **Authentication**: Create test users with proper tokens
3. **Cleanup**: Clean up test data between tests
4. **Real Scenarios**: Test complete user workflows

### Common Patterns

#### Testing Controllers
```typescript
describe('UsersController', () => {
  let controller: UsersController;
  let service: jest.Mocked<UsersService>;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      controllers: [UsersController],
      providers: [
        { provide: UsersService, useValue: mockService },
      ],
    }).compile();

    controller = module.get<UsersController>(UsersController);
    service = module.get(UsersService);
  });

  it('should create user', async () => {
    service.create.mockResolvedValue(mockUser);
    const result = await controller.create(createUserDto);
    expect(result).toEqual(expectedResponse);
  });
});
```

#### Testing Services
```typescript
describe('UsersService', () => {
  let service: UsersService;
  let model: jest.Mocked<Model<UserDocument>>;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [
        UsersService,
        { provide: getModelToken(User.name), useValue: mockModel },
      ],
    }).compile();

    service = module.get<UsersService>(UsersService);
    model = module.get(getModelToken(User.name));
  });
});
```

#### Testing E2E Endpoints
```typescript
describe('POST /users', () => {
  it('should create user', async () => {
    const response = await request(app.getHttpServer())
      .post('/users')
      .set('Authorization', `Bearer ${adminToken}`)
      .send(createUserDto)
      .expect(201);

    expect(response.body.success).toBe(true);
    expect(response.body.data).toMatchObject(expectedUser);
  });
});
```

## Coverage Requirements

The project maintains the following coverage thresholds:
- **Branches**: 80%
- **Functions**: 80%
- **Lines**: 80%
- **Statements**: 80%

## Debugging Tests

### VS Code Configuration
Add to `.vscode/launch.json`:
```json
{
  "type": "node",
  "request": "launch",
  "name": "Debug Jest Tests",
  "program": "${workspaceFolder}/node_modules/.bin/jest",
  "args": ["--runInBand"],
  "console": "integratedTerminal",
  "internalConsoleOptions": "neverOpen"
}
```

### Command Line Debugging
```bash
# Debug specific test
npm run test:debug -- --testNamePattern="should create user"

# Debug with breakpoints
node --inspect-brk node_modules/.bin/jest --runInBand
```

## Continuous Integration

Tests are automatically run in CI/CD pipeline:
1. Unit tests run on every commit
2. E2E tests run on pull requests
3. Coverage reports are generated and uploaded
4. Tests must pass before merging

## Troubleshooting

### Common Issues

1. **MongoDB Connection**: Ensure MongoDB Memory Server is properly configured
2. **JWT Tokens**: Check JWT secret and expiration settings
3. **Async Operations**: Use proper async/await patterns
4. **Mock Cleanup**: Clear mocks between tests to avoid interference

### Performance Tips

1. Use `--runInBand` for debugging
2. Use `--maxWorkers=1` for memory-constrained environments
3. Mock heavy operations like database calls
4. Use `beforeAll`/`afterAll` for expensive setup/teardown

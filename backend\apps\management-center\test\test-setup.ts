import { MongoMemoryServer } from 'mongodb-memory-server';
import { Connection } from 'mongoose';

let mongod: MongoMemoryServer;

beforeAll(async () => {
  // Start in-memory MongoDB instance for testing
  mongod = await MongoMemoryServer.create();
  process.env.MONGODB_URI_WRITE = mongod.getUri();
  process.env.MONGODB_URI_READ = mongod.getUri();
  process.env.JWT_SECRET = 'test-jwt-secret';
  process.env.JWT_EXPIRES_IN = '1h';
});

afterAll(async () => {
  // Clean up
  if (mongod) {
    await mongod.stop();
  }
});

// Global test utilities
global.testUtils = {
  createTestUser: (overrides = {}) => ({
    email: '<EMAIL>',
    password: 'password123',
    firstName: 'Test',
    lastName: 'User',
    role: 'user',
    ...overrides,
  }),
  
  createTestAdmin: (overrides = {}) => ({
    email: '<EMAIL>',
    password: 'password123',
    firstName: 'Admin',
    lastName: 'User',
    role: 'admin',
    ...overrides,
  }),
};

// Extend Jest matchers
expect.extend({
  toBeValidObjectId(received) {
    const pass = /^[0-9a-fA-F]{24}$/.test(received);
    if (pass) {
      return {
        message: () => `expected ${received} not to be a valid ObjectId`,
        pass: true,
      };
    } else {
      return {
        message: () => `expected ${received} to be a valid ObjectId`,
        pass: false,
      };
    }
  },
});

declare global {
  namespace jest {
    interface Matchers<R> {
      toBeValidObjectId(): R;
    }
  }
  
  var testUtils: {
    createTestUser: (overrides?: any) => any;
    createTestAdmin: (overrides?: any) => any;
  };
}

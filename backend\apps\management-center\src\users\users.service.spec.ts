import { Test, TestingModule } from '@nestjs/testing';
import { getModelToken } from '@nestjs/mongoose';
import { NotFoundException } from '@nestjs/common';
import { Model, Types } from 'mongoose';
import { UsersService } from './users.service';
import { User, UserDocument, UserRole, UserStatus } from '../auth/schemas/user.schema';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';

// Mock PaginationDto
interface MockPaginationDto {
  page?: number;
  limit?: number;
}

describe('UsersService', () => {
  let service: UsersService;
  let userWriteModel: jest.Mocked<Model<UserDocument>>;
  let userReadModel: jest.Mocked<Model<UserDocument>>;

  const mockUser = {
    _id: new Types.ObjectId('507f1f77bcf86cd799439011'),
    email: '<EMAIL>',
    password: 'hashedpassword',
    firstName: 'John',
    lastName: 'Doe',
    role: UserRole.USER,
    status: UserStatus.ACTIVE,
    emailVerified: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    get fullName() { return `${this.firstName} ${this.lastName}`; },
    isActive: () => true,
    hasRole: (role: UserRole) => role === UserRole.USER,
    hasMinimumRole: (role: UserRole) => true,
  } as any;

  const mockExecFunction = jest.fn();
  const mockQuery = {
    find: jest.fn().mockReturnThis(),
    findOne: jest.fn().mockReturnThis(),
    findOneAndUpdate: jest.fn().mockReturnThis(),
    countDocuments: jest.fn().mockReturnThis(),
    skip: jest.fn().mockReturnThis(),
    limit: jest.fn().mockReturnThis(),
    sort: jest.fn().mockReturnThis(),
    exec: mockExecFunction,
  };

  beforeEach(async () => {
    const mockUserModel = {
      find: jest.fn().mockReturnValue(mockQuery),
      findOne: jest.fn().mockReturnValue(mockQuery),
      findOneAndUpdate: jest.fn().mockReturnValue(mockQuery),
      countDocuments: jest.fn().mockReturnValue(mockQuery),
      create: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      providers: [
        UsersService,
        {
          provide: getModelToken(User.name, 'write'),
          useValue: mockUserModel,
        },
        {
          provide: getModelToken(User.name, 'read'),
          useValue: mockUserModel,
        },
      ],
    }).compile();

    service = module.get<UsersService>(UsersService);
    userWriteModel = module.get(getModelToken(User.name, 'write'));
    userReadModel = module.get(getModelToken(User.name, 'read'));
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('should be defined', () => {
    expect(service).toBeDefined();
  });

  describe('findAll', () => {
    it('should return paginated users', async () => {
      const pagination: MockPaginationDto = { page: 1, limit: 10 };
      const mockUsers = [mockUser];
      const mockTotal = 1;

      mockExecFunction
        .mockResolvedValueOnce(mockUsers) // for find query
        .mockResolvedValueOnce(mockTotal); // for countDocuments query

      const result = await service.findAll(pagination);

      expect(userReadModel.find).toHaveBeenCalledWith({ deletedAt: { $exists: false } });
      expect(mockQuery.skip).toHaveBeenCalledWith(0);
      expect(mockQuery.limit).toHaveBeenCalledWith(10);
      expect(mockQuery.sort).toHaveBeenCalledWith({ createdAt: -1 });
      expect(result).toEqual({ users: mockUsers, total: mockTotal });
    });

    it('should use default pagination when not provided', async () => {
      const mockUsers = [mockUser];
      const mockTotal = 1;

      mockExecFunction
        .mockResolvedValueOnce(mockUsers)
        .mockResolvedValueOnce(mockTotal);

      const result = await service.findAll();

      expect(mockQuery.skip).toHaveBeenCalledWith(0);
      expect(mockQuery.limit).toHaveBeenCalledWith(10);
      expect(result).toEqual({ users: mockUsers, total: mockTotal });
    });

    it('should calculate correct skip value for pagination', async () => {
      const pagination: MockPaginationDto = { page: 3, limit: 5 };
      const mockUsers = [mockUser];
      const mockTotal = 15;

      mockExecFunction
        .mockResolvedValueOnce(mockUsers)
        .mockResolvedValueOnce(mockTotal);

      await service.findAll(pagination);

      expect(mockQuery.skip).toHaveBeenCalledWith(10); // (3-1) * 5
      expect(mockQuery.limit).toHaveBeenCalledWith(5);
    });
  });

  describe('findOne', () => {
    it('should return a user by valid ID', async () => {
      const userId = '507f1f77bcf86cd799439011';
      mockExecFunction.mockResolvedValue(mockUser);

      const result = await service.findOne(userId);

      expect(userReadModel.findOne).toHaveBeenCalledWith({
        _id: userId,
        deletedAt: { $exists: false },
      });
      expect(result).toEqual(mockUser);
    });

    it('should throw NotFoundException for invalid ID format', async () => {
      const invalidId = 'invalid-id';

      await expect(service.findOne(invalidId)).rejects.toThrow(
        new NotFoundException('Invalid user ID format')
      );
    });

    it('should throw NotFoundException when user not found', async () => {
      const userId = '507f1f77bcf86cd799439011';
      mockExecFunction.mockResolvedValue(null);

      await expect(service.findOne(userId)).rejects.toThrow(
        new NotFoundException('User not found')
      );
    });
  });

  describe('findByEmail', () => {
    it('should return user by email', async () => {
      const email = '<EMAIL>';
      mockExecFunction.mockResolvedValue(mockUser);

      const result = await service.findByEmail(email);

      expect(userReadModel.findOne).toHaveBeenCalledWith({
        email,
        deletedAt: { $exists: false },
      });
      expect(result).toEqual(mockUser);
    });

    it('should return null when user not found by email', async () => {
      const email = '<EMAIL>';
      mockExecFunction.mockResolvedValue(null);

      const result = await service.findByEmail(email);

      expect(result).toBeNull();
    });
  });

  describe('findByOrganization', () => {
    it('should return users by organization ID', async () => {
      const organizationId = '507f1f77bcf86cd799439012';
      const pagination: MockPaginationDto = { page: 1, limit: 10 };
      const mockUsers = [mockUser];
      const mockTotal = 1;

      mockExecFunction
        .mockResolvedValueOnce(mockUsers)
        .mockResolvedValueOnce(mockTotal);

      const result = await service.findByOrganization(organizationId, pagination);

      expect(userReadModel.find).toHaveBeenCalledWith({
        organizationId: new Types.ObjectId(organizationId),
        deletedAt: { $exists: false },
      });
      expect(result).toEqual({ users: mockUsers, total: mockTotal });
    });

    it('should throw NotFoundException for invalid organization ID', async () => {
      const invalidId = 'invalid-id';

      await expect(service.findByOrganization(invalidId)).rejects.toThrow(
        new NotFoundException('Invalid organization ID format')
      );
    });
  });

  describe('update', () => {
    it('should update user successfully', async () => {
      const userId = '507f1f77bcf86cd799439011';
      const updateDto: UpdateUserDto = {
        firstName: 'Updated',
        lastName: 'Name',
      };
      const updatedUser = { ...mockUser, ...updateDto };

      mockExecFunction.mockResolvedValue(updatedUser);

      const result = await service.update(userId, updateDto);

      expect(userWriteModel.findOneAndUpdate).toHaveBeenCalledWith(
        { _id: userId, deletedAt: { $exists: false } },
        { ...updateDto, updatedAt: expect.any(Date) },
        { new: true }
      );
      expect(result).toEqual(updatedUser);
    });

    it('should throw NotFoundException for invalid user ID', async () => {
      const invalidId = 'invalid-id';
      const updateDto: UpdateUserDto = { firstName: 'Updated' };

      await expect(service.update(invalidId, updateDto)).rejects.toThrow(
        new NotFoundException('Invalid user ID format')
      );
    });

    it('should throw NotFoundException when user not found', async () => {
      const userId = '507f1f77bcf86cd799439011';
      const updateDto: UpdateUserDto = { firstName: 'Updated' };

      mockExecFunction.mockResolvedValue(null);

      await expect(service.update(userId, updateDto)).rejects.toThrow(
        new NotFoundException('User not found')
      );
    });
  });

  describe('updateStatus', () => {
    it('should update user status successfully', async () => {
      const userId = '507f1f77bcf86cd799439011';
      const status = UserStatus.SUSPENDED;
      const updatedUser = { ...mockUser, status };

      mockExecFunction.mockResolvedValue(updatedUser);

      const result = await service.updateStatus(userId, status);

      expect(userWriteModel.findOneAndUpdate).toHaveBeenCalledWith(
        { _id: userId, deletedAt: { $exists: false } },
        { status, updatedAt: expect.any(Date) },
        { new: true }
      );
      expect(result).toEqual(updatedUser);
    });
  });

  describe('verifyEmail', () => {
    it('should verify user email successfully', async () => {
      const userId = '507f1f77bcf86cd799439011';
      const verifiedUser = {
        ...mockUser,
        emailVerified: true,
        status: UserStatus.ACTIVE,
      };

      mockExecFunction.mockResolvedValue(verifiedUser);

      const result = await service.verifyEmail(userId);

      expect(userWriteModel.findOneAndUpdate).toHaveBeenCalledWith(
        { _id: userId, deletedAt: { $exists: false } },
        {
          emailVerified: true,
          status: UserStatus.ACTIVE,
          emailVerificationToken: undefined,
          emailVerificationExpires: undefined,
          updatedAt: expect.any(Date),
        },
        { new: true }
      );
      expect(result).toEqual(verifiedUser);
    });
  });

  describe('remove', () => {
    it('should soft delete user successfully', async () => {
      const userId = '507f1f77bcf86cd799439011';
      const deletedUser = { ...mockUser, deletedAt: new Date() };

      mockExecFunction.mockResolvedValue(deletedUser);

      await service.remove(userId);

      expect(userWriteModel.findOneAndUpdate).toHaveBeenCalledWith(
        { _id: userId, deletedAt: { $exists: false } },
        {
          deletedAt: expect.any(Date),
          updatedAt: expect.any(Date),
        }
      );
    });

    it('should throw NotFoundException when user not found for deletion', async () => {
      const userId = '507f1f77bcf86cd799439011';
      mockExecFunction.mockResolvedValue(null);

      await expect(service.remove(userId)).rejects.toThrow(
        new NotFoundException('User not found')
      );
    });
  });

  describe('getActiveUsersCount', () => {
    it('should return count of active users', async () => {
      const mockCount = 5;
      mockExecFunction.mockResolvedValue(mockCount);

      const result = await service.getActiveUsersCount();

      expect(userReadModel.countDocuments).toHaveBeenCalledWith({
        status: UserStatus.ACTIVE,
        deletedAt: { $exists: false },
      });
      expect(result).toBe(mockCount);
    });
  });

  describe('getUsersByRole', () => {
    it('should return users by role', async () => {
      const role = UserRole.ADMIN;
      const mockUsers = [mockUser];
      mockExecFunction.mockResolvedValue(mockUsers);

      const result = await service.getUsersByRole(role);

      expect(userReadModel.find).toHaveBeenCalledWith({
        role,
        deletedAt: { $exists: false },
      });
      expect(mockQuery.sort).toHaveBeenCalledWith({ createdAt: -1 });
      expect(result).toEqual(mockUsers);
    });
  });

  describe('searchUsers', () => {
    it('should search users by query', async () => {
      const query = 'john';
      const pagination: MockPaginationDto = { page: 1, limit: 10 };
      const mockUsers = [mockUser];
      const mockTotal = 1;

      mockExecFunction
        .mockResolvedValueOnce(mockUsers)
        .mockResolvedValueOnce(mockTotal);

      const result = await service.searchUsers(query, pagination);

      expect(userReadModel.find).toHaveBeenCalledWith({
        $or: [
          { firstName: expect.any(RegExp) },
          { lastName: expect.any(RegExp) },
          { email: expect.any(RegExp) },
        ],
        deletedAt: { $exists: false },
      });
      expect(result).toEqual({ users: mockUsers, total: mockTotal });
    });

    it('should create case-insensitive regex for search', async () => {
      const query = 'John';
      const mockUsers = [mockUser];
      const mockTotal = 1;

      mockExecFunction
        .mockResolvedValueOnce(mockUsers)
        .mockResolvedValueOnce(mockTotal);

      await service.searchUsers(query);

      const expectedRegex = new RegExp(query, 'i');
      expect(userReadModel.find).toHaveBeenCalledWith({
        $or: [
          { firstName: expectedRegex },
          { lastName: expectedRegex },
          { email: expectedRegex },
        ],
        deletedAt: { $exists: false },
      });
    });
  });
});

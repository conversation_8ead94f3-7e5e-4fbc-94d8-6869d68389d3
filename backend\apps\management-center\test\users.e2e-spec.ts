import { Test, TestingModule } from '@nestjs/testing';
import { INestApplication, ValidationPipe } from '@nestjs/common';
import { MongooseModule } from '@nestjs/mongoose';
import { JwtModule } from '@nestjs/jwt';
import { PassportModule } from '@nestjs/passport';
import { CqrsModule } from '@nestjs/cqrs';
import * as request from 'supertest';
import { Connection } from 'mongoose';
import { getConnectionToken } from '@nestjs/mongoose';

import { UsersModule } from '../src/users/users.module';
import { AuthModule } from '../src/auth/auth.module';
import { UserRole, UserStatus } from '../src/auth/schemas/user.schema';
import { CreateUserDto } from '../src/users/dto/create-user.dto';
import { UpdateUserDto } from '../src/users/dto/update-user.dto';

describe('Users (e2e)', () => {
  let app: INestApplication;
  let connection: Connection;
  let adminToken: string;
  let userToken: string;
  let adminUserId: string;
  let regularUserId: string;

  beforeAll(async () => {
    const moduleFixture: TestingModule = await Test.createTestingModule({
      imports: [
        MongooseModule.forRoot(process.env.MONGODB_URI_WRITE, {
          connectionName: 'write',
        }),
        MongooseModule.forRoot(process.env.MONGODB_URI_READ, {
          connectionName: 'read',
        }),
        PassportModule,
        JwtModule.register({
          secret: process.env.JWT_SECRET,
          signOptions: { expiresIn: process.env.JWT_EXPIRES_IN },
        }),
        CqrsModule,
        AuthModule,
        UsersModule,
      ],
    }).compile();

    app = moduleFixture.createNestApplication();
    app.useGlobalPipes(new ValidationPipe());
    
    connection = moduleFixture.get<Connection>(getConnectionToken('write'));
    
    await app.init();

    // Create test users and get tokens
    await setupTestData();
  });

  afterAll(async () => {
    // Clean up database
    await connection.db.dropDatabase();
    await connection.close();
    await app.close();
  });

  beforeEach(async () => {
    // Clean up users collection before each test (except setup users)
    await connection.collection('users').deleteMany({
      email: { $nin: ['<EMAIL>', '<EMAIL>'] }
    });
  });

  async function setupTestData() {
    // Register admin user
    const adminData = {
      email: '<EMAIL>',
      password: 'password123',
      firstName: 'Admin',
      lastName: 'User',
      role: UserRole.ADMIN,
    };

    const adminResponse = await request(app.getHttpServer())
      .post('/auth/register')
      .send(adminData)
      .expect(201);

    adminUserId = adminResponse.body.data.user.id;

    // Manually verify admin email and activate
    await connection.collection('users').updateOne(
      { email: adminData.email },
      { 
        $set: { 
          emailVerified: true, 
          status: UserStatus.ACTIVE,
          emailVerificationToken: undefined,
        }
      }
    );

    // Login admin to get token
    const adminLoginResponse = await request(app.getHttpServer())
      .post('/auth/login')
      .send({
        email: adminData.email,
        password: adminData.password,
      })
      .expect(200);

    adminToken = adminLoginResponse.body.data.accessToken;

    // Register regular user
    const userData = {
      email: '<EMAIL>',
      password: 'password123',
      firstName: 'Regular',
      lastName: 'User',
      role: UserRole.USER,
    };

    const userResponse = await request(app.getHttpServer())
      .post('/auth/register')
      .send(userData)
      .expect(201);

    regularUserId = userResponse.body.data.user.id;

    // Manually verify user email and activate
    await connection.collection('users').updateOne(
      { email: userData.email },
      { 
        $set: { 
          emailVerified: true, 
          status: UserStatus.ACTIVE,
          emailVerificationToken: undefined,
        }
      }
    );

    // Login user to get token
    const userLoginResponse = await request(app.getHttpServer())
      .post('/auth/login')
      .send({
        email: userData.email,
        password: userData.password,
      })
      .expect(200);

    userToken = userLoginResponse.body.data.accessToken;
  }

  describe('POST /users', () => {
    it('should create a new user as admin', async () => {
      const createUserDto: CreateUserDto = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'New',
        lastName: 'User',
        role: UserRole.USER,
      };

      const response = await request(app.getHttpServer())
        .post('/users')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(createUserDto)
        .expect(201);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('User created successfully');
      expect(response.body.data).toMatchObject({
        email: createUserDto.email,
        firstName: createUserDto.firstName,
        lastName: createUserDto.lastName,
        role: createUserDto.role,
      });
      expect(response.body.data.password).toBeUndefined();
    });

    it('should reject user creation with invalid data', async () => {
      const invalidUserDto = {
        email: 'invalid-email',
        password: '123', // Too short
        firstName: '',
        lastName: '',
        role: 'invalid-role',
      };

      await request(app.getHttpServer())
        .post('/users')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(invalidUserDto)
        .expect(400);
    });

    it('should reject user creation without admin privileges', async () => {
      const createUserDto: CreateUserDto = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'New',
        lastName: 'User',
        role: UserRole.USER,
      };

      await request(app.getHttpServer())
        .post('/users')
        .set('Authorization', `Bearer ${userToken}`)
        .send(createUserDto)
        .expect(403);
    });

    it('should reject user creation without authentication', async () => {
      const createUserDto: CreateUserDto = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'New',
        lastName: 'User',
        role: UserRole.USER,
      };

      await request(app.getHttpServer())
        .post('/users')
        .send(createUserDto)
        .expect(401);
    });
  });

  describe('GET /users', () => {
    it('should get all users as admin', async () => {
      const response = await request(app.getHttpServer())
        .get('/users')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Users retrieved successfully');
      expect(response.body.data.users).toBeInstanceOf(Array);
      expect(response.body.data.total).toBeGreaterThanOrEqual(2);
    });

    it('should support pagination', async () => {
      const response = await request(app.getHttpServer())
        .get('/users?page=1&limit=1')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.data.users).toHaveLength(1);
      expect(response.body.data.total).toBeGreaterThanOrEqual(2);
    });

    it('should reject access without proper role', async () => {
      await request(app.getHttpServer())
        .get('/users')
        .set('Authorization', `Bearer ${userToken}`)
        .expect(403);
    });
  });

  describe('GET /users/search', () => {
    it('should search users by query', async () => {
      const response = await request(app.getHttpServer())
        .get('/users/search?q=admin')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Users search completed');
      expect(response.body.data.users).toBeInstanceOf(Array);
    });

    it('should return empty results for non-matching query', async () => {
      const response = await request(app.getHttpServer())
        .get('/users/search?q=nonexistent')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.data.users).toHaveLength(0);
      expect(response.body.data.total).toBe(0);
    });
  });

  describe('GET /users/:id', () => {
    it('should allow user to view their own profile', async () => {
      const response = await request(app.getHttpServer())
        .get(`/users/${regularUserId}`)
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.email).toBe('<EMAIL>');
    });

    it('should redirect regular user to their own profile when accessing others', async () => {
      const response = await request(app.getHttpServer())
        .get(`/users/${adminUserId}`)
        .set('Authorization', `Bearer ${userToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('User profile retrieved successfully');
      expect(response.body.data.email).toBe('<EMAIL>'); // Their own profile
    });

    it('should allow admin to view any user profile', async () => {
      const response = await request(app.getHttpServer())
        .get(`/users/${regularUserId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.email).toBe('<EMAIL>');
    });

    it('should return 404 for non-existent user', async () => {
      const fakeId = '507f1f77bcf86cd799439011';
      await request(app.getHttpServer())
        .get(`/users/${fakeId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(404);
    });

    it('should return 400 for invalid user ID format', async () => {
      await request(app.getHttpServer())
        .get('/users/invalid-id')
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(404); // NotFoundException from service
    });
  });

  describe('PUT /users/:id', () => {
    it('should allow user to update their own profile', async () => {
      const updateDto: UpdateUserDto = {
        firstName: 'Updated',
        lastName: 'Name',
      };

      const response = await request(app.getHttpServer())
        .put(`/users/${regularUserId}`)
        .set('Authorization', `Bearer ${userToken}`)
        .send(updateDto)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.firstName).toBe('Updated');
      expect(response.body.data.lastName).toBe('Name');
    });

    it('should redirect regular user to update their own profile', async () => {
      const updateDto: UpdateUserDto = {
        firstName: 'Updated',
        lastName: 'Name',
      };

      const response = await request(app.getHttpServer())
        .put(`/users/${adminUserId}`)
        .set('Authorization', `Bearer ${userToken}`)
        .send(updateDto)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('Profile updated successfully');
      expect(response.body.data.firstName).toBe('Updated');
    });

    it('should allow admin to update any user', async () => {
      const updateDto: UpdateUserDto = {
        firstName: 'AdminUpdated',
        lastName: 'User',
      };

      const response = await request(app.getHttpServer())
        .put(`/users/${regularUserId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .send(updateDto)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.data.firstName).toBe('AdminUpdated');
    });

    it('should validate update data', async () => {
      const invalidUpdateDto = {
        email: 'invalid-email',
      };

      await request(app.getHttpServer())
        .put(`/users/${regularUserId}`)
        .set('Authorization', `Bearer ${userToken}`)
        .send(invalidUpdateDto)
        .expect(400);
    });
  });

  describe('DELETE /users/:id', () => {
    it('should delete user as admin', async () => {
      // Create a user to delete
      const createUserDto: CreateUserDto = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'To',
        lastName: 'Delete',
        role: UserRole.USER,
      };

      const createResponse = await request(app.getHttpServer())
        .post('/users')
        .set('Authorization', `Bearer ${adminToken}`)
        .send(createUserDto)
        .expect(201);

      const userIdToDelete = createResponse.body.data.id;

      // Delete the user
      const response = await request(app.getHttpServer())
        .delete(`/users/${userIdToDelete}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(200);

      expect(response.body.success).toBe(true);
      expect(response.body.message).toBe('User deleted successfully');

      // Verify user is soft deleted
      await request(app.getHttpServer())
        .get(`/users/${userIdToDelete}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(404);
    });

    it('should reject deletion without admin privileges', async () => {
      await request(app.getHttpServer())
        .delete(`/users/${adminUserId}`)
        .set('Authorization', `Bearer ${userToken}`)
        .expect(403);
    });

    it('should return 404 for non-existent user deletion', async () => {
      const fakeId = '507f1f77bcf86cd799439011';
      await request(app.getHttpServer())
        .delete(`/users/${fakeId}`)
        .set('Authorization', `Bearer ${adminToken}`)
        .expect(404);
    });
  });

  describe('Authentication and Authorization', () => {
    it('should reject requests without token', async () => {
      await request(app.getHttpServer())
        .get('/users')
        .expect(401);
    });

    it('should reject requests with invalid token', async () => {
      await request(app.getHttpServer())
        .get('/users')
        .set('Authorization', 'Bearer invalid-token')
        .expect(401);
    });

    it('should reject requests with expired token', async () => {
      // This would require creating an expired token, which is complex
      // In a real scenario, you might mock the JWT service or use a very short expiry
      // For now, we'll skip this test or implement it with proper JWT mocking
    });
  });
});

#!/bin/bash

# Test runner script for Management Center
# This script runs all tests and generates comprehensive reports

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="Management Center"
COVERAGE_DIR="coverage"
REPORTS_DIR="test-reports"

echo -e "${BLUE}🧪 Starting test suite for ${PROJECT_NAME}${NC}"
echo "=================================================="

# Create reports directory
mkdir -p ${REPORTS_DIR}

# Function to print section headers
print_section() {
    echo ""
    echo -e "${BLUE}$1${NC}"
    echo "$(printf '=%.0s' {1..50})"
}

# Function to handle errors
handle_error() {
    echo -e "${RED}❌ Error: $1${NC}"
    exit 1
}

# Function to print success
print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

# Function to print warning
print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

# Check if Node.js and npm are available
print_section "Checking Prerequisites"
if ! command -v node &> /dev/null; then
    handle_error "Node.js is not installed"
fi

if ! command -v npm &> /dev/null; then
    handle_error "npm is not installed"
fi

NODE_VERSION=$(node --version)
NPM_VERSION=$(npm --version)
print_success "Node.js version: $NODE_VERSION"
print_success "npm version: $NPM_VERSION"

# Install dependencies if needed
print_section "Installing Dependencies"
if [ ! -d "node_modules" ]; then
    echo "Installing dependencies..."
    npm install || handle_error "Failed to install dependencies"
    print_success "Dependencies installed"
else
    print_success "Dependencies already installed"
fi

# Clean previous reports
print_section "Cleaning Previous Reports"
rm -rf ${COVERAGE_DIR}
rm -rf ${REPORTS_DIR}
mkdir -p ${REPORTS_DIR}
print_success "Previous reports cleaned"

# Run linting
print_section "Running Linter"
if npm run lint; then
    print_success "Linting passed"
else
    print_warning "Linting failed - continuing with tests"
fi

# Run unit tests
print_section "Running Unit Tests"
echo "Running unit tests with coverage..."

if npm run test:cov -- --reporters=default --reporters=jest-junit --outputFile=${REPORTS_DIR}/unit-test-results.xml; then
    print_success "Unit tests passed"
    
    # Check coverage thresholds
    if [ -f "${COVERAGE_DIR}/coverage-summary.json" ]; then
        echo "Coverage summary:"
        cat ${COVERAGE_DIR}/coverage-summary.json | jq '.total'
        print_success "Coverage report generated"
    fi
else
    handle_error "Unit tests failed"
fi

# Run E2E tests
print_section "Running E2E Tests"
echo "Starting E2E tests..."

# Check if MongoDB is available (for local testing)
if command -v mongod &> /dev/null; then
    print_success "MongoDB found - using local instance"
else
    print_warning "MongoDB not found - using in-memory database"
fi

if npm run test:e2e -- --reporters=default --reporters=jest-junit --outputFile=${REPORTS_DIR}/e2e-test-results.xml; then
    print_success "E2E tests passed"
else
    handle_error "E2E tests failed"
fi

# Generate test summary
print_section "Generating Test Summary"

# Create HTML report
cat > ${REPORTS_DIR}/test-summary.html << EOF
<!DOCTYPE html>
<html>
<head>
    <title>Test Summary - ${PROJECT_NAME}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f0f0f0; padding: 20px; border-radius: 5px; }
        .section { margin: 20px 0; padding: 15px; border-left: 4px solid #007acc; }
        .success { border-left-color: #28a745; }
        .warning { border-left-color: #ffc107; }
        .error { border-left-color: #dc3545; }
        .stats { display: flex; gap: 20px; }
        .stat { background: #f8f9fa; padding: 10px; border-radius: 5px; text-align: center; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 5px; overflow-x: auto; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Test Summary - ${PROJECT_NAME}</h1>
        <p>Generated on: $(date)</p>
        <p>Node.js: $NODE_VERSION | npm: $NPM_VERSION</p>
    </div>

    <div class="section success">
        <h2>✅ Test Results</h2>
        <p>All tests completed successfully!</p>
        <div class="stats">
            <div class="stat">
                <strong>Unit Tests</strong><br>
                <span style="color: #28a745;">PASSED</span>
            </div>
            <div class="stat">
                <strong>E2E Tests</strong><br>
                <span style="color: #28a745;">PASSED</span>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>📊 Coverage Report</h2>
        <p>Coverage reports are available in the <code>coverage/</code> directory.</p>
        <p><a href="../coverage/lcov-report/index.html">View HTML Coverage Report</a></p>
    </div>

    <div class="section">
        <h2>📁 Generated Files</h2>
        <ul>
            <li><code>coverage/</code> - Coverage reports</li>
            <li><code>test-reports/unit-test-results.xml</code> - Unit test results (JUnit format)</li>
            <li><code>test-reports/e2e-test-results.xml</code> - E2E test results (JUnit format)</li>
        </ul>
    </div>
</body>
</html>
EOF

print_success "Test summary generated: ${REPORTS_DIR}/test-summary.html"

# Print final summary
print_section "Test Summary"
echo -e "${GREEN}🎉 All tests completed successfully!${NC}"
echo ""
echo "📊 Reports generated:"
echo "  • Coverage: ${COVERAGE_DIR}/lcov-report/index.html"
echo "  • Summary: ${REPORTS_DIR}/test-summary.html"
echo "  • Unit Tests: ${REPORTS_DIR}/unit-test-results.xml"
echo "  • E2E Tests: ${REPORTS_DIR}/e2e-test-results.xml"
echo ""
echo -e "${BLUE}Next steps:${NC}"
echo "  • Review coverage report for areas needing improvement"
echo "  • Check test results for any warnings or skipped tests"
echo "  • Consider adding more edge case tests"
echo ""
echo -e "${GREEN}✨ Testing complete!${NC}"

import { Test, TestingModule } from '@nestjs/testing';
import { CommandBus, QueryBus } from '@nestjs/cqrs';
import { NotFoundException } from '@nestjs/common';
import { UsersController } from './users.controller';
import { UsersService } from './users.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { CreateUserCommand } from './commands/create-user.command';
import { UserRole, UserStatus } from '../auth/schemas/user.schema';

// Mock ApiResponseDto since the library might not be available
class MockApiResponseDto {
  static success(data: any, message: string) {
    return { success: true, data, message };
  }
  static error(message: string, statusCode: number) {
    return { success: false, message, statusCode };
  }
}

// Mock PaginationDto
interface MockPaginationDto {
  page?: number;
  limit?: number;
}

describe('UsersController', () => {
  let controller: UsersController;
  let usersService: jest.Mocked<UsersService>;
  let commandBus: jest.Mocked<CommandBus>;
  let queryBus: jest.Mocked<QueryBus>;

  const mockUser = {
    _id: '507f1f77bcf86cd799439011',
    email: '<EMAIL>',
    password: 'hashedpassword',
    firstName: 'John',
    lastName: 'Doe',
    role: UserRole.USER,
    status: UserStatus.ACTIVE,
    emailVerified: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    get fullName() { return `${this.firstName} ${this.lastName}`; },
    isActive: () => true,
    hasRole: (role: UserRole) => role === UserRole.USER,
    hasMinimumRole: (role: UserRole) => true,
  } as any;

  const mockAdmin = {
    _id: '507f1f77bcf86cd799439012',
    email: '<EMAIL>',
    password: 'hashedpassword',
    firstName: 'Admin',
    lastName: 'User',
    role: UserRole.ADMIN,
    status: UserStatus.ACTIVE,
    emailVerified: true,
    createdAt: new Date(),
    updatedAt: new Date(),
    get fullName() { return `${this.firstName} ${this.lastName}`; },
    isActive: () => true,
    hasRole: (role: UserRole) => role === UserRole.ADMIN,
    hasMinimumRole: (role: UserRole) => true,
  } as any;

  beforeEach(async () => {
    const mockUsersService = {
      findAll: jest.fn(),
      findOne: jest.fn(),
      searchUsers: jest.fn(),
      update: jest.fn(),
      remove: jest.fn(),
    };

    const mockCommandBus = {
      execute: jest.fn(),
    };

    const mockQueryBus = {
      execute: jest.fn(),
    };

    const module: TestingModule = await Test.createTestingModule({
      controllers: [UsersController],
      providers: [
        {
          provide: UsersService,
          useValue: mockUsersService,
        },
        {
          provide: CommandBus,
          useValue: mockCommandBus,
        },
        {
          provide: QueryBus,
          useValue: mockQueryBus,
        },
      ],
    }).compile();

    controller = module.get<UsersController>(UsersController);
    usersService = module.get(UsersService);
    commandBus = module.get(CommandBus);
    queryBus = module.get(QueryBus);
  });

  it('should be defined', () => {
    expect(controller).toBeDefined();
  });

  describe('create', () => {
    it('should create a new user successfully', async () => {
      const createUserDto: CreateUserDto = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'New',
        lastName: 'User',
        role: UserRole.USER,
      };

      const createdUser = { ...mockUser, ...createUserDto };
      commandBus.execute.mockResolvedValue(createdUser);

      const result = await controller.create(createUserDto);

      expect(commandBus.execute).toHaveBeenCalledWith(
        expect.any(CreateUserCommand)
      );
      expect(result).toEqual(
        MockApiResponseDto.success(createdUser, 'User created successfully')
      );
    });

    it('should handle command bus errors', async () => {
      const createUserDto: CreateUserDto = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'New',
        lastName: 'User',
        role: UserRole.USER,
      };

      commandBus.execute.mockRejectedValue(new Error('Database error'));

      await expect(controller.create(createUserDto)).rejects.toThrow(
        'Database error'
      );
    });
  });

  describe('findAll', () => {
    it('should return paginated users list', async () => {
      const pagination: MockPaginationDto = { page: 1, limit: 10 };
      const mockResult = {
        users: [mockUser],
        total: 1,
      };

      usersService.findAll.mockResolvedValue(mockResult);

      const result = await controller.findAll(pagination as any);

      expect(usersService.findAll).toHaveBeenCalledWith(pagination);
      expect(result).toEqual(
        MockApiResponseDto.success(mockResult, 'Users retrieved successfully')
      );
    });

    it('should handle service errors', async () => {
      const pagination: MockPaginationDto = { page: 1, limit: 10 };
      usersService.findAll.mockRejectedValue(new Error('Database error'));

      await expect(controller.findAll(pagination as any)).rejects.toThrow(
        'Database error'
      );
    });
  });

  describe('searchUsers', () => {
    it('should search users successfully', async () => {
      const query = 'john';
      const pagination: MockPaginationDto = { page: 1, limit: 10 };
      const mockResult = {
        users: [mockUser],
        total: 1,
      };

      usersService.searchUsers.mockResolvedValue(mockResult);

      const result = await controller.searchUsers(query, pagination as any);

      expect(usersService.searchUsers).toHaveBeenCalledWith(query, pagination);
      expect(result).toEqual(
        MockApiResponseDto.success(mockResult, 'Users search completed')
      );
    });
  });

  describe('findOne', () => {
    it('should allow user to view their own profile', async () => {
      const userId = mockUser._id;
      const currentUserId = mockUser._id;
      const currentUserRole = UserRole.USER;

      usersService.findOne.mockResolvedValue(mockUser as any);

      const result = await controller.findOne(userId, currentUserId, currentUserRole);

      expect(usersService.findOne).toHaveBeenCalledWith(userId);
      expect(result).toEqual(
        MockApiResponseDto.success(mockUser, 'User retrieved successfully')
      );
    });

    it('should redirect regular user to their own profile when accessing others', async () => {
      const userId = '507f1f77bcf86cd799439013'; // Different user ID
      const currentUserId = mockUser._id;
      const currentUserRole = UserRole.USER;

      usersService.findOne.mockResolvedValue(mockUser as any);

      const result = await controller.findOne(userId, currentUserId, currentUserRole);

      expect(usersService.findOne).toHaveBeenCalledWith(currentUserId);
      expect(result).toEqual(
        MockApiResponseDto.success(mockUser, 'User profile retrieved successfully')
      );
    });

    it('should allow admin to view any user profile', async () => {
      const userId = mockUser._id;
      const currentUserId = mockAdmin._id;
      const currentUserRole = UserRole.ADMIN;

      usersService.findOne.mockResolvedValue(mockUser as any);

      const result = await controller.findOne(userId, currentUserId, currentUserRole);

      expect(usersService.findOne).toHaveBeenCalledWith(userId);
      expect(result).toEqual(
        MockApiResponseDto.success(mockUser, 'User retrieved successfully')
      );
    });

    it('should handle user not found', async () => {
      const userId = '507f1f77bcf86cd799439013';
      const currentUserId = mockUser._id;
      const currentUserRole = UserRole.ADMIN;

      usersService.findOne.mockRejectedValue(new NotFoundException('User not found'));

      await expect(
        controller.findOne(userId, currentUserId, currentUserRole)
      ).rejects.toThrow(NotFoundException);
    });
  });

  describe('update', () => {
    it('should allow user to update their own profile', async () => {
      const userId = mockUser._id;
      const currentUserId = mockUser._id;
      const currentUserRole = UserRole.USER;
      const updateUserDto: UpdateUserDto = {
        firstName: 'Updated',
        lastName: 'Name',
      };

      const updatedUser = { ...mockUser, ...updateUserDto };
      usersService.update.mockResolvedValue(updatedUser as any);

      const result = await controller.update(
        userId,
        updateUserDto,
        currentUserId,
        currentUserRole
      );

      expect(usersService.update).toHaveBeenCalledWith(userId, updateUserDto);
      expect(result).toEqual(
        MockApiResponseDto.success(updatedUser, 'User updated successfully')
      );
    });

    it('should redirect regular user to update their own profile', async () => {
      const userId = '507f1f77bcf86cd799439013'; // Different user ID
      const currentUserId = mockUser._id;
      const currentUserRole = UserRole.USER;
      const updateUserDto: UpdateUserDto = {
        firstName: 'Updated',
        lastName: 'Name',
      };

      const updatedUser = { ...mockUser, ...updateUserDto };
      usersService.update.mockResolvedValue(updatedUser as any);

      const result = await controller.update(
        userId,
        updateUserDto,
        currentUserId,
        currentUserRole
      );

      expect(usersService.update).toHaveBeenCalledWith(currentUserId, updateUserDto);
      expect(result).toEqual(
        MockApiResponseDto.success(updatedUser, 'Profile updated successfully')
      );
    });

    it('should allow admin to update any user', async () => {
      const userId = mockUser._id;
      const currentUserId = mockAdmin._id;
      const currentUserRole = UserRole.ADMIN;
      const updateUserDto: UpdateUserDto = {
        firstName: 'Updated',
        lastName: 'Name',
      };

      const updatedUser = { ...mockUser, ...updateUserDto };
      usersService.update.mockResolvedValue(updatedUser as any);

      const result = await controller.update(
        userId,
        updateUserDto,
        currentUserId,
        currentUserRole
      );

      expect(usersService.update).toHaveBeenCalledWith(userId, updateUserDto);
      expect(result).toEqual(
        MockApiResponseDto.success(updatedUser, 'User updated successfully')
      );
    });
  });

  describe('remove', () => {
    it('should delete user successfully', async () => {
      const userId = mockUser._id;
      usersService.remove.mockResolvedValue();

      const result = await controller.remove(userId);

      expect(usersService.remove).toHaveBeenCalledWith(userId);
      expect(result).toEqual(
        MockApiResponseDto.success(null, 'User deleted successfully')
      );
    });

    it('should handle user not found during deletion', async () => {
      const userId = '507f1f77bcf86cd799439013';
      usersService.remove.mockRejectedValue(new NotFoundException('User not found'));

      await expect(controller.remove(userId)).rejects.toThrow(NotFoundException);
    });
  });

  describe('hasElevatedPermissions', () => {
    it('should return true for elevated roles', () => {
      expect(controller['hasElevatedPermissions'](UserRole.MANAGER)).toBe(true);
      expect(controller['hasElevatedPermissions'](UserRole.ADMIN)).toBe(true);
      expect(controller['hasElevatedPermissions'](UserRole.SUPER_ADMIN)).toBe(true);
    });

    it('should return false for regular user role', () => {
      expect(controller['hasElevatedPermissions'](UserRole.USER)).toBe(false);
    });
  });

  describe('Microservice message patterns', () => {
    it('should handle get_users message', async () => {
      const data = { page: 1, limit: 10 };
      const mockResult = { users: [mockUser], total: 1 };
      usersService.findAll.mockResolvedValue(mockResult);

      const result = await controller.handleGetUsers(data);

      expect(usersService.findAll).toHaveBeenCalledWith(data);
      expect(result).toEqual(mockResult);
    });

    it('should handle get_user_by_id message', async () => {
      const data = { id: mockUser._id };
      usersService.findOne.mockResolvedValue(mockUser as any);

      const result = await controller.handleGetUserById(data);

      expect(usersService.findOne).toHaveBeenCalledWith(data.id);
      expect(result).toEqual(mockUser);
    });

    it('should handle create_user message', async () => {
      const data: CreateUserDto = {
        email: '<EMAIL>',
        password: 'password123',
        firstName: 'New',
        lastName: 'User',
        role: UserRole.USER,
      };

      const createdUser = { ...mockUser, ...data };
      commandBus.execute.mockResolvedValue(createdUser);

      const result = await controller.handleCreateUser(data);

      expect(commandBus.execute).toHaveBeenCalledWith(expect.any(CreateUserCommand));
      expect(result).toEqual(createdUser);
    });
  });
});
